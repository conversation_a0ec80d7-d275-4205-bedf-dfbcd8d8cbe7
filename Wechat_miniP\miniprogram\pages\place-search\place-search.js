// place-search.js
Component({
  data: {
    // 搜索相关
    searchKeyword: '',
    searchMode: 'name', // 'name' | 'type' | 'nearby'

    // 起始位置
    startLocation: '',
    startVertexId: null,
    locationSuggestions: [],
    showLocationSuggestions: false,

    // 搜索条件
    selectedType: '',
    searchDistance: 1000,

    // 结果
    searchResults: [],
    loading: false,

    // 类型选项
    typeOptions: [
      { label: '全部类型', value: '' },
      { label: '景点', value: '景点' },
      { label: '餐厅', value: '餐厅' },
      { label: '酒店', value: '酒店' },
      { label: '购物', value: '购物' },
      { label: '交通', value: '交通' },
      { label: '娱乐', value: '娱乐' },
      { label: '医疗', value: '医疗' },
      { label: '教育', value: '教育' }
    ],

    // 距离选项
    distanceOptions: [
      { label: '500米内', value: 500 },
      { label: '1公里内', value: 1000 },
      { label: '2公里内', value: 2000 },
      { label: '5公里内', value: 5000 },
      { label: '10公里内', value: 10000 }
    ]
  },

  pageLifetimes: {
    show: function() {
      if (typeof this.getTabBar === 'function' && this.getTabBar()) {
        this.getTabBar().setData({
          selected: 1
        });
      }
    }
  },

  onLoad: function() {
    // 页面加载时测试API连接
    this.testApiConnection();
  },

  methods: {
    // 测试API连接
    testApiConnection: function() {
      console.log('API连接测试 - JavaScript版本');
    },

    // 搜索模式切换
    onSearchModeChange: function(e) {
      this.setData({
        searchMode: e.detail.value,
        searchResults: []
      });
    },

    // 起始位置输入
    onStartLocationInput: function(e) {
      const value = e.detail.value;
      this.setData({ startLocation: value });

      if (value.trim()) {
        this.getLocationSuggestions(value);
      } else {
        this.setData({
          locationSuggestions: [],
          showLocationSuggestions: false
        });
      }
    },

    // 获取位置建议
    getLocationSuggestions: function(query) {
      // 简化版本，实际应该调用API
      console.log('获取位置建议:', query);
    },

    // 选择起始位置
    onSelectStartLocation: function(e) {
      const index = e.currentTarget.dataset.index;
      const suggestion = this.data.locationSuggestions[index];

      this.setData({
        startLocation: suggestion.name,
        startVertexId: suggestion.vertex_id,
        showLocationSuggestions: false
      });
    },

    // 搜索关键词输入
    onSearchInput: function(e) {
      this.setData({ searchKeyword: e.detail.value });
    },

    // 类型选择
    onTypeChange: function(e) {
      const index = e.detail.value;
      const selectedOption = this.data.typeOptions[index];
      this.setData({ selectedType: selectedOption.value });
    },

    // 距离选择
    onDistanceChange: function(e) {
      const index = e.detail.value;
      const selectedOption = this.data.distanceOptions[index];
      this.setData({ searchDistance: selectedOption.value });
    },

    // 执行搜索
    handleSearch: function() {
      const that = this;
      const searchKeyword = this.data.searchKeyword;
      const selectedType = this.data.selectedType;
      const searchMode = this.data.searchMode;
      const startVertexId = this.data.startVertexId;

      if (!searchKeyword.trim() && !selectedType && searchMode !== 'nearby') {
        wx.showToast({
          title: '请输入搜索条件',
          icon: 'none'
        });
        return;
      }

      if (searchMode === 'nearby' && !startVertexId) {
        wx.showToast({
          title: '请先选择起始位置',
          icon: 'none'
        });
        return;
      }

      this.setData({ loading: true });

      // 实际API调用
      wx.request({
        url: 'http://localhost:5000/api/path/search-spots-by-name',
        method: 'POST',
        data: {
          name: searchKeyword,
          type: selectedType
        },
        success: function(res) {
          if (res.data.success) {
            that.setData({
              searchResults: res.data.data || [],
              loading: false
            });
          } else {
            wx.showToast({
              title: '搜索失败',
              icon: 'none'
            });
            that.setData({ loading: false });
          }
        },
        fail: function(error) {
          console.error('搜索失败:', error);
          wx.showToast({
            title: '网络错误',
            icon: 'none'
          });
          that.setData({ loading: false });
        }
      });
    },

    // 查看景点详情
    onSpotTap: function(e) {
      const index = e.currentTarget.dataset.index;
      const spot = this.data.searchResults[index];

      // 跳转到景点详情页面
      wx.navigateTo({
        url: '/pages/place-detail/place-detail?id=' + spot.vertex_id + '&name=' + spot.name
      });
    }
  }
})
