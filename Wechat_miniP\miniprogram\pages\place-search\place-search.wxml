<!--place-search.wxml-->
<view class="page">
  <!-- 导航栏 -->
  <view class="nav-bar">
    <view class="nav-bar-title">景点搜索</view>
  </view>

  <scroll-view class="scrollarea" scroll-y type="list">
    <!-- 搜索模式选择 -->
    <view class="search-mode-section">
      <view class="section-title">搜索方式</view>
      <radio-group bindchange="onSearchModeChange">
        <label class="radio-item">
          <radio value="name" checked="{{searchMode === 'name'}}" />
          <text>按名称搜索</text>
        </label>
        <label class="radio-item">
          <radio value="type" checked="{{searchMode === 'type'}}" />
          <text>按类型搜索</text>
        </label>
        <label class="radio-item">
          <radio value="nearby" checked="{{searchMode === 'nearby'}}" />
          <text>附近搜索</text>
        </label>
      </radio-group>
    </view>

    <!-- 起始位置选择 -->
    <view class="location-section" wx:if="{{searchMode === 'type' || searchMode === 'nearby'}}">
      <view class="section-title">起始位置</view>
      <view class="input-container">
        <input
          class="location-input"
          type="text"
          placeholder="输入起始位置..."
          value="{{startLocation}}"
          bindinput="onStartLocationInput"
        />
        <!-- 位置建议列表 -->
        <view class="suggestions-list" wx:if="{{showLocationSuggestions}}">
          <view
            class="suggestion-item"
            wx:for="{{locationSuggestions}}"
            wx:key="vertex_id"
            data-index="{{index}}"
            bindtap="onSelectStartLocation"
          >
            <view class="suggestion-name">{{item.name}}</view>
            <view class="suggestion-type">{{item.type}}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 搜索条件 -->
    <view class="search-conditions">
      <!-- 关键词搜索 -->
      <view class="condition-item" wx:if="{{searchMode === 'name' || searchMode === 'type'}}">
        <view class="condition-label">搜索关键词</view>
        <input
          class="condition-input"
          type="text"
          placeholder="{{searchMode === 'name' ? '输入景点名称...' : '输入关键词...'}}"
          value="{{searchKeyword}}"
          bindinput="onSearchInput"
        />
      </view>

      <!-- 类型选择 -->
      <view class="condition-item" wx:if="{{searchMode === 'type'}}">
        <view class="condition-label">场所类型</view>
        <picker
          class="condition-picker"
          mode="selector"
          range="{{typeOptions}}"
          range-key="label"
          bindchange="onTypeChange"
        >
          <view class="picker-text">
            {{selectedType || '选择类型'}}
          </view>
        </picker>
      </view>

      <!-- 搜索距离 -->
      <view class="condition-item" wx:if="{{searchMode === 'type' || searchMode === 'nearby'}}">
        <view class="condition-label">搜索范围</view>
        <picker
          class="condition-picker"
          mode="selector"
          range="{{distanceOptions}}"
          range-key="label"
          bindchange="onDistanceChange"
        >
          <view class="picker-text">1公里内</view>
        </picker>
      </view>
    </view>

    <!-- 搜索按钮 -->
    <view class="search-button-container">
      <button
        class="search-button"
        bindtap="handleSearch"
        loading="{{loading}}"
        disabled="{{loading}}"
      >
        {{loading ? '搜索中...' : '开始搜索'}}
      </button>
    </view>

    <!-- 搜索结果 -->
    <view class="results-section" wx:if="{{searchResults.length > 0}}">
      <view class="section-title">搜索结果 ({{searchResults.length}})</view>
      <view class="results-list">
        <view
          class="result-item"
          wx:for="{{searchResults}}"
          wx:key="vertex_id"
          data-index="{{index}}"
          bindtap="onSpotTap"
        >
          <view class="result-info">
            <view class="result-name">{{item.name}}</view>
            <view class="result-type">{{item.type}}</view>
            <view class="result-distance" wx:if="{{item.path_distance}}">
              距离: {{item.path_distance}}米
            </view>
          </view>
          <view class="result-arrow">></view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{searchResults.length === 0 && !loading}}">
      <view class="empty-icon">🔍</view>
      <view class="empty-text">选择搜索方式，开始探索景点</view>
    </view>
  </scroll-view>
</view>
