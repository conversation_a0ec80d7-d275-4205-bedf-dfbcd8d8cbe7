// api.ts - 微信小程序API工具类
import { getApiBaseUrl } from '../config/api';

interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

interface LocationSuggestion {
  vertex_id: number;
  name: string;
  type: string;
  x: number;
  y: number;
}

interface SearchSpotParams {
  name?: string;
  type?: string;
  startVertexId?: number;
  distance?: number;
  limit?: number;
}

interface SpotResult {
  vertex_id: number;
  name: string;
  type: string;
  x: number;
  y: number;
  path_distance?: number;
  description?: string;
}

class ApiService {
  private baseUrl = getApiBaseUrl(); // 使用配置文件中的API地址

  /**
   * 发送HTTP请求
   */
  private request<T>(options: {
    url: string;
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
    data?: any;
    header?: any;
  }): Promise<ApiResponse<T>> {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${this.baseUrl}${options.url}`,
        method: options.method || 'GET',
        data: options.data,
        header: {
          'Content-Type': 'application/json',
          ...options.header
        },
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data as ApiResponse<T>);
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${res.data}`));
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  }

  /**
   * 获取地点建议（自动完成）
   */
  async getLocationSuggestions(query: string, limit: number = 10): Promise<LocationSuggestion[]> {
    try {
      const response = await this.request<LocationSuggestion[]>({
        url: `/path/location-suggestions?q=${encodeURIComponent(query)}&limit=${limit}`,
        method: 'GET'
      });
      return response.data || [];
    } catch (error) {
      console.error('获取地点建议失败:', error);
      return [];
    }
  }

  /**
   * 按名称搜索景点
   */
  async searchSpotsByName(params: { name: string; type?: string }): Promise<SpotResult[]> {
    try {
      const queryParams = new URLSearchParams();
      queryParams.append('name', params.name);
      if (params.type) {
        queryParams.append('type', params.type);
      }

      const response = await this.request<SpotResult[]>({
        url: `/path/search-by-name?${queryParams.toString()}`,
        method: 'GET'
      });
      return response.data || [];
    } catch (error) {
      console.error('按名称搜索景点失败:', error);
      return [];
    }
  }

  /**
   * 多条件查询景点
   */
  async getSpotsByCriteria(params: {
    startVertexId?: number;
    locationName?: string;
    distance?: number;
    types?: string[];
    keywords?: string[];
    limit?: number;
  }): Promise<{ nearby_spots: SpotResult[] }> {
    try {
      const requestBody = {
        start_vertex_id: params.startVertexId,
        location_name: params.locationName,
        distance: params.distance || 1000,
        types: params.types || [],
        keywords: params.keywords || [],
        limit: params.limit || 15
      };
      
      const response = await this.request<{ nearby_spots: SpotResult[] }>({
        url: '/path/spots-by-criteria',
        method: 'POST',
        data: requestBody
      });
      return response.data || { nearby_spots: [] };
    } catch (error) {
      console.error('多条件查询景点失败:', error);
      return { nearby_spots: [] };
    }
  }

  /**
   * 按起始点搜索附近景点
   */
  async searchSpotsByStart(params: {
    startVertexId: number;
    locationName?: string;
    distance?: number;
    limit?: number;
  }): Promise<{ nearby_spots: SpotResult[] }> {
    try {
      const requestBody = {
        start_vertex_id: params.startVertexId,
        location_name: params.locationName,
        distance: params.distance || 1000,
        limit: params.limit || 200,
        page: 1,
        per_page: 200
      };
      
      const response = await this.request<{ nearby_spots: SpotResult[] }>({
        url: '/path/spots-by-start',
        method: 'POST',
        data: requestBody
      });
      return response.data || { nearby_spots: [] };
    } catch (error) {
      console.error('按起始点搜索景点失败:', error);
      return { nearby_spots: [] };
    }
  }

  /**
   * 获取热门景点推荐
   */
  async getPopularSpots(limit: number = 20): Promise<SpotResult[]> {
    try {
      const response = await this.request<SpotResult[]>({
        url: '/recommend/popular',
        method: 'GET',
        data: { limit }
      });
      return response.data || [];
    } catch (error) {
      console.error('获取热门景点失败:', error);
      return [];
    }
  }
}

// 导出单例
export const apiService = new ApiService();
export default apiService;
