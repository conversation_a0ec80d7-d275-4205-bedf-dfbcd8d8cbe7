# 协同过滤功能使用指南

## 概述

本指南介绍如何在个性化旅游系统中使用协同过滤推荐功能。系统在景点推荐、游记社区、美食推荐三个模块中都实现了协同过滤算法。

## 功能入口

### 1. 景点推荐模块
- **页面路径**: `/recommend`
- **功能位置**: 推荐方式选择器中的"与您相似的用户推荐"标签
- **使用条件**: 需要用户登录

### 2. 游记社区模块
- **页面路径**: `/diary`
- **功能位置**: 排序选项中的"为您推荐"
- **使用条件**: 需要用户登录

### 3. 美食推荐模块
- **页面路径**: `/food`
- **功能位置**: 推荐方式选择器中的"智能推荐"
- **使用条件**: 需要用户登录

## 使用步骤

### 景点推荐协同过滤

1. **登录系统**
   - 访问登录页面并完成登录
   - 确保用户状态为已登录

2. **访问景点推荐页面**
   - 点击导航栏中的"景点推荐"
   - 或直接访问 `/recommend`

3. **选择协同过滤推荐**
   - 在页面顶部的推荐方式选择器中
   - 点击"与您相似的用户推荐"标签
   - 系统会自动加载基于协同过滤的景点推荐

4. **查看推荐结果**
   - 推荐结果会显示推荐原因
   - 包含相似用户偏好信息
   - 可以看到推荐算法的执行时间

### 游记社区协同过滤

1. **登录系统**
   - 确保用户已登录

2. **访问游记社区页面**
   - 点击导航栏中的"游记社区"
   - 或直接访问 `/diary`

3. **选择为您推荐**
   - 在排序选项中选择"为您推荐"
   - 系统会根据用户的浏览、点赞、收藏、评论行为
   - 使用协同过滤算法推荐相关文章

4. **查看推荐结果**
   - 推荐的文章会优先显示
   - 系统会显示推荐类型和执行时间
   - 文章按照推荐得分排序

### 美食推荐协同过滤

1. **登录系统**
   - 确保用户已登录

2. **访问美食推荐页面**
   - 点击导航栏中的"美食推荐"
   - 或直接访问 `/food`

3. **选择智能推荐**
   - 在推荐方式选择器中选择"智能推荐"
   - 对于已登录用户，系统会自动使用协同过滤算法

4. **查看推荐结果**
   - 推荐的餐馆会显示推荐类型标识
   - 包含协同过滤得分信息
   - 支持菜系筛选和排序

## 推荐原理

### 数据收集
- **景点推荐**: 基于用户浏览景点的历史记录
- **游记社区**: 基于用户对文章的浏览、点赞、收藏、评论行为
- **美食推荐**: 基于用户对餐馆的收藏和评分记录

### 相似度计算
- 使用余弦相似度算法计算用户间的相似性
- 考虑用户的行为模式和偏好特征
- 动态更新用户相似度矩阵

### 推荐生成
- 基于相似用户的偏好生成推荐列表
- 过滤用户已经交互过的内容
- 按照推荐得分排序返回结果

## 降级策略

当协同过滤无法提供推荐时，系统会自动降级：

1. **景点推荐**: 降级到热门景点推荐
2. **游记社区**: 降级到热门文章推荐
3. **美食推荐**: 降级到菜系偏好推荐，再降级到热门餐馆推荐

## 用户体验优化

### 推荐标识
- 系统会明确标识推荐类型（协同过滤、热门推荐等）
- 显示推荐算法的执行时间
- 提供推荐原因说明

### 性能优化
- 使用缓存机制减少计算时间
- 采用堆排序算法优化Top-K推荐
- 支持实时数据更新

### 容错处理
- 新用户自动使用热门推荐
- API异常时自动降级
- 提供友好的错误提示

## 功能验证

### 验证方法
- 直接在各个模块中使用协同过滤功能
- 观察推荐结果的个性化程度
- 查看推荐算法的执行时间和类型标识

### 验证步骤
1. 登录用户账号
2. 在景点推荐、游记社区、美食推荐中使用相应功能
3. 观察推荐结果是否符合个人偏好
4. 检查推荐类型标识和执行时间

## 常见问题

### Q: 为什么没有看到协同过滤推荐？
A: 请确保：
- 用户已登录
- 用户有足够的历史行为数据
- 系统中有其他用户的行为数据用于计算相似度

### Q: 推荐结果为什么是热门内容？
A: 可能的原因：
- 用户是新用户，没有足够的行为数据
- 没有找到相似的用户
- 系统自动降级到热门推荐

### Q: 如何提高推荐准确性？
A: 建议：
- 多浏览、点赞、收藏感兴趣的内容
- 对餐馆进行评分
- 增加用户行为数据的多样性

### Q: 推荐结果多久更新一次？
A:
- 用户行为数据实时收集
- 推荐算法支持实时计算
- 相似度矩阵定期更新

## 技术支持

如果在使用过程中遇到问题，可以：
1. 查看浏览器控制台的错误信息
2. 运行后端测试脚本验证功能状态
3. 检查网络连接和后端服务状态

## 更新日志

- **v1.0**: 实现基础协同过滤功能
- **v1.1**: 添加降级策略和容错处理
- **v1.2**: 优化性能和用户体验
- **v1.3**: 添加测试工具和使用指南
