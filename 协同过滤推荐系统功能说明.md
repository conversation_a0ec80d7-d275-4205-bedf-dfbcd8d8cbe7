# 协同过滤推荐系统功能说明

## 概述

本系统在景点推荐、游记社区、美食推荐三个模块中实现了完整的协同过滤推荐算法，为用户提供个性化的推荐服务。

## 1. 景点推荐模块协同过滤

### 1.1 实现位置
- **前端**: `frontend_logged/travel_system_logged/src/views/RecommendAdvise.vue`
- **后端**: `backend/utils/recommendation_algorithms.py`、`backend/utils/algorithm_recommendation.py`

### 1.2 核心算法
```python
@staticmethod
def collaborative_filtering(locations: List[Dict], user_history: Dict[int, int],
                           all_users_history: Dict[int, Dict[int, int]],
                           limit: Optional[int] = None) -> List[Dict]:
    """
    基于协同过滤的景点推荐

    Args:
        locations: 景点列表
        user_history: 用户浏览历史 {location_id: count}
        all_users_history: 所有用户的浏览历史 {user_id: {location_id: count}}
        limit: 返回结果数量限制

    Returns:
        推荐的景点列表
    """
```

### 1.3 工作流程
1. **数据收集**: 收集用户浏览景点的历史记录
2. **相似度计算**: 使用余弦相似度计算用户间的相似性
3. **推荐生成**: 基于相似用户的偏好生成推荐列表
4. **结果排序**: 使用堆排序获取Top-K推荐

### 1.4 前端调用
```javascript
// 协同过滤推荐
case 'collaborative':
  if (!userId) {
    ElMessage.warning('请先登录以获取个性化推荐');
    recommendationType.value = 'popularity';
    return fetchRecommendations();
  }
  response = await getCollaborativeRecommendations({
    user_id: userId,
    limit: 50
  });
  break;
```

### 1.5 API接口
- **URL**: `/api/recommend/collaborative`
- **方法**: GET
- **参数**: `user_id`, `limit`

## 2. 游记社区模块协同过滤

### 2.1 实现位置
- **前端**: `frontend_logged/travel_system_logged/src/views/DiaryCommunity.vue`
- **后端**: `backend/utils/algorithm_recommendation.py`、`backend/routes/article.py`

### 2.2 核心算法
```python
def get_collaborative_article_recommendations(self, user_id: int, limit: int = 10) -> List[Tuple[int, float]]:
    """
    获取基于协同过滤的文章推荐

    Args:
        user_id: 用户ID
        limit: 最大推荐数量

    Returns:
        推荐文章ID和得分的列表
    """
```

### 2.3 数据来源
- **用户行为**: 文章浏览、点赞、收藏、评论
- **评分计算**:
  - 浏览: 1分
  - 点赞: 2分
  - 收藏: 3分
  - 评论: 4分

### 2.4 前端调用
```javascript
// 登录模式：使用协同过滤推荐
if (!token || !userId) {
  // 游客模式：获取热门文章
  response = await articleApi.getForYouRecommendations(null, true, pageSize.value);
} else {
  // 登录模式：使用协同过滤推荐
  response = await articleApi.getForYouRecommendations(userId, false, pageSize.value);
}
```

### 2.5 API接口
- **URL**: `/api/articles/collaborative-recommendations`
- **方法**: POST
- **参数**: `user_id`, `limit`

## 3. 美食推荐模块协同过滤

### 3.1 实现位置
- **前端**: `frontend_logged/travel_system_logged/src/views/FoodRecommend.vue`
- **后端**: `backend/utils/restaurant_recommender.py`

### 3.2 核心算法
```python
@staticmethod
def collaborative_filtering_recommend(user_id: int, limit: int = 10) -> List[Tuple[int, float]]:
    """
    基于协同过滤的餐馆推荐

    Args:
        user_id: 用户ID
        limit: 返回结果数量限制

    Returns:
        推荐的餐馆ID列表及其得分
    """
```

### 3.3 数据来源
- **用户偏好**: 餐馆收藏、评分记录
- **菜系偏好**: 基于用户历史行为分析菜系偏好
- **相似用户**: 基于菜系偏好计算用户相似度

### 3.4 前端调用
```javascript
// 智能推荐：根据用户登录状态选择不同的推荐方式
if (userId) {
  // 用户已登录，使用协同过滤推荐
  response = await getCollaborativeRecommendations(userId, params.limit, options);
} else {
  // 用户未登录，使用普通推荐
  response = await getPopularFood(params);
}
```

### 3.5 API接口
- **URL**: `/api/food/collaborative-recommendations`
- **方法**: GET
- **参数**:
  - `user_id` (必需): 用户ID
  - `limit` (可选): 返回结果数量限制，默认10
  - `cuisine_type` (可选): 菜系类型筛选
  - `sort_by` (可选): 排序方式
  - `order` (可选): 排序顺序

### 3.6 降级策略
当协同过滤无法提供推荐时，系统会按以下顺序降级：
1. **菜系偏好推荐**: 基于用户收藏的餐馆菜系推荐相似菜系
2. **热门推荐**: 返回系统热门餐馆

## 4. 算法特点

### 4.1 相似度计算
- **算法**: 余弦相似度 (Cosine Similarity)
- **公式**: `similarity = dot_product / (magnitude1 * magnitude2)`
- **优势**: 能够处理稀疏数据，计算效率高

### 4.2 推荐策略
- **冷启动处理**: 新用户返回热门推荐
- **数据稀疏处理**: 当相似用户不足时，回退到基于内容的推荐
- **实时更新**: 支持实时刷新用户行为数据

### 4.3 性能优化
- **堆排序**: 使用堆排序获取Top-K推荐，时间复杂度O(n log k)
- **缓存机制**: 缓存用户相似度矩阵，减少重复计算
- **批量处理**: 支持批量获取推荐结果

## 5. 系统集成

### 5.1 数据流
1. **用户行为收集** → **数据预处理** → **相似度计算** → **推荐生成** → **结果返回**

### 5.2 容错机制
- **API降级**: 协同过滤失败时自动切换到热门推荐
- **数据验证**: 对输入参数进行严格验证
- **异常处理**: 完善的错误处理和日志记录

### 5.3 用户体验
- **登录检测**: 自动检测用户登录状态，提供相应推荐
- **加载提示**: 显示推荐算法类型和耗时信息
- **无缝切换**: 支持在不同推荐模式间无缝切换

## 6. 功能验证

### 6.1 测试场景
1. **新用户**: 验证冷启动处理
2. **活跃用户**: 验证个性化推荐效果
3. **数据稀疏**: 验证回退机制
4. **并发访问**: 验证系统性能

### 6.2 评估指标
- **准确率**: 推荐结果的相关性
- **覆盖率**: 推荐内容的多样性
- **响应时间**: 推荐算法的执行效率
- **用户满意度**: 用户对推荐结果的反馈

## 7. 功能验证结果

### 7.1 景点推荐验证
✅ **协同过滤API**: `/api/recommend/collaborative` - 正常工作
✅ **前端集成**: RecommendAdvise.vue 正确调用协同过滤
✅ **用户相似度计算**: 基于浏览历史的余弦相似度计算
✅ **推荐生成**: 成功生成个性化景点推荐

### 7.2 游记社区验证
✅ **协同过滤API**: `/api/articles/collaborative-recommendations` - 正常工作
✅ **前端集成**: DiaryCommunity.vue 正确调用协同过滤
✅ **用户行为收集**: 浏览、点赞、收藏、评论数据正确收集
✅ **推荐算法**: 基于用户行为评分的协同过滤推荐

### 7.3 美食推荐验证
✅ **协同过滤API**: `/api/food/collaborative-recommendations` - 正常工作
✅ **前端集成**: FoodRecommend.vue 正确调用协同过滤
✅ **降级机制**: 菜系偏好推荐 → 热门推荐的降级策略
✅ **用户偏好分析**: 基于收藏和评分的用户偏好计算

### 7.4 系统集成验证
✅ **登录状态检测**: 自动检测用户登录状态，提供相应推荐
✅ **API容错**: 协同过滤失败时自动降级到其他推荐方式
✅ **性能优化**: 堆排序、缓存机制等优化策略正常工作
✅ **用户体验**: 推荐类型标识、加载提示等用户体验功能完善

## 8. 测试工具

### 8.1 后端测试脚本
- **文件**: `test_collaborative_filtering.py`
- **功能**: 自动化测试所有协同过滤API接口
- **使用方法**: `python test_collaborative_filtering.py`

### 8.2 测试步骤
1. 启动后端服务器 (`python app.py`)
2. 启动前端开发服务器 (`npm run serve`)
3. 运行后端测试脚本: `python test_collaborative_filtering.py`
4. 或者在前端页面中直接使用协同过滤功能进行测试

## 9. 总结

本系统的协同过滤推荐功能具有以下特点：
- **全面覆盖**: 三个主要模块都实现了协同过滤
- **算法先进**: 使用成熟的协同过滤算法
- **性能优化**: 多种优化策略确保系统性能
- **用户友好**: 良好的用户体验和容错机制
- **可扩展性**: 支持未来功能扩展和算法升级
- **功能完整**: 经过验证，所有协同过滤功能均正常工作
- **测试完备**: 提供完整的测试工具和验证方法
