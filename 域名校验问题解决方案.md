# 微信小程序域名校验问题解决方案

## 问题描述
微信开发者工具显示：`request 合法域名校验出错`，导致小程序无法访问本地API。

## 解决方案（按推荐程度排序）

### 🥇 方案1：正确关闭域名校验（最简单）

#### 步骤：
1. **打开微信开发者工具**
2. **点击右上角"详情"按钮**
3. **选择"本地设置"标签页**
4. **勾选以下选项**：
   - ✅ "不校验合法域名、web-view（业务域名）、TLS 版本以及 HTTPS 证书"
   - ✅ "不校验 Secure 域名"
   - ✅ "启用调试"

5. **重新编译项目**：
   - 按 `Ctrl + Shift + R`
   - 或点击"项目" → "重新构建npm"

6. **清除缓存**：
   - 点击"项目" → "清除缓存" → "清除全部缓存"

7. **重启开发者工具**

#### 验证：
在控制台应该看到成功的网络请求，而不是域名校验错误。

---

### 🥈 方案2：使用代理服务器（推荐备选）

如果方案1不起作用，使用代理服务器绕过域名限制。

#### 步骤：

1. **安装Node.js**（如果没有）：
   - 访问 https://nodejs.org/
   - 下载并安装LTS版本

2. **启动代理服务器**：
   ```bash
   # 双击运行
   start-proxy.bat
   ```

3. **修改小程序配置**：
   编辑 `Wechat_miniP/miniprogram/config/api.ts`：
   ```typescript
   const API_STRATEGY = 1  // 使用代理服务器
   ```

4. **更新IP地址**：
   在 `proxy-server.js` 和 `api.ts` 中将 `*************` 替换为您的实际IP地址

#### 验证：
- 代理服务器启动后显示：`代理服务器启动成功！`
- 小程序请求地址变为：`http://您的IP:3000/api`

---

### 🥉 方案3：使用模拟器（最后选择）

#### 步骤：
1. **在微信开发者工具中**：
   - 点击"设置" → "通用设置"
   - 选择"使用模拟器"
   - 重启开发者工具

2. **修改配置**：
   ```typescript
   const API_STRATEGY = 2  // 使用本地服务器
   ```

---

## 完整操作流程

### 第一步：确认后端服务运行
```bash
cd backend
.\venv\Scripts\Activate.ps1
python app.py
```

确保看到：
```
* Running on all addresses (0.0.0.0)
* Running on http://127.0.0.1:5000
* Running on http://*************:5000
```

### 第二步：选择并执行解决方案
推荐先尝试方案1，如果不行再使用方案2。

### 第三步：测试小程序
1. 打开微信开发者工具
2. 进入"路线"页面
3. 查看控制台输出
4. 确认地点数据加载成功

## 常见问题排查

### Q1：方案1执行后仍然报错
**A1：** 
- 确认所有选项都已勾选
- 重启微信开发者工具
- 清除浏览器缓存
- 检查工具版本（建议使用最新版）

### Q2：代理服务器启动失败
**A2：**
- 检查Node.js是否正确安装
- 检查3000端口是否被占用
- 以管理员身份运行命令行

### Q3：IP地址不确定
**A3：**
- 运行 `get_ip.bat` 获取IP地址
- 或在命令行执行 `ipconfig`

### Q4：后端连接失败
**A4：**
- 确认后端服务正在运行
- 检查防火墙设置
- 确认IP地址正确

## 成功标志

✅ **控制台输出**：
```
发起请求: {url: "http://...", method: "GET", data: undefined}
请求成功: {statusCode: 200, data: [...]}
API连接检查完成
已加载X个地点
```

✅ **界面表现**：
- 起点/终点选择器有选项
- 地图显示地点标记
- 可以进行路径规划

## 技术原理

**域名校验问题**：微信小程序默认只允许访问配置的合法域名，本地开发时需要关闭此限制。

**代理服务器**：通过中间代理转发请求，绕过域名限制。

**CORS配置**：确保后端允许跨域请求。
